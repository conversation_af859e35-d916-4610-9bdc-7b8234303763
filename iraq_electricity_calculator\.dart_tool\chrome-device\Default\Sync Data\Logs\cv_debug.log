{"logTime": "1006/101250", "correlationVector":"hVm3jgd2oMOlXLQFgtUNZL.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southindia_prod-s01-004-apac-southindia", "migrationStage":"NotStarted", "server":"akswtt01800004l"}}
{"logTime": "1006/101250", "correlationVector":"hVm3jgd2oMOlXLQFgtUNZL.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Encryption Keys", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "1006/101251", "correlationVector":"PObL9ZmSNl72Z1hPMria5G","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"", "migrationStage":"", "server":""}}
{"logTime": "1006/101251", "correlationVector":"PObL9ZmSNl72Z1hPMria5G.0","action":"EncryptionKeysFetcher.DownloadKeys:", "result":"Success", "context":Key count: 6, Last key timestamp: 2025-05-23T11:21:26Z}
{"logTime": "1006/101251", "correlationVector":"PObL9ZmSNl72Z1hPMria5G.1","action":"EncryptionKeysManager::SetEncryptionKeys:", "result":"Success", "context":Key names[6]:[Pa86AJ4HVwcFxe8AY0wp++NyRU4nJuqNBgzdIZV4CnmhBMefbvTkr7ReOiSHkQPmTgXlwGb7JcqteZDhwlhqNg==][b7CexGILI7cy5K5vb+TWwOJC+uggnlWiVgWh2puXJlwZEfuqAbFi7wb61TqSZh0Jk+jqcNiFl6ALpOP87ELiJA==][CYa2fzX9uJq0L31WBK8WOCMCAL3Wprt29uHFlyaMnipIrI4sgecd6I/0oqgiOUsVDOwNbKUEVIXdXgGu5z6qiQ==][1GMXOy1Q4YgHmG3/f+YM28jE7veF1cj1Cr5m2Uz8GWx30SdJ7Ipl9Fbp54oSLr8/rqeVaxUes3A4jwqst8b3rg==][EnnFVIQAf1S4jPqsoBRHb2pxzvPumZ9qllyl4xBC/UtaWbe5k3mbUTA1aHsNW3T7oYpF1gcFJI2BYbwfQB+PXw==][0irP34rAOZ3qlSwOIUAwkg/Equteh7y4J7w2baew7dwMHE4uzLqh7snmvGhhTMmhV8x5Sqd6cHhfMXeAjyzg9g==]}
{"logTime": "1006/101251", "correlationVector":"PObL9ZmSNl72Z1hPMria5G.2","action":"EncryptionKeysManager::SetEncryptionKeysWithTimestamps:", "result":"Success", "context":Key timestamps[6]:[2023-05-08T19:09:52Z][2023-11-15T16:58:38Z][2024-05-17T06:44:06Z][2024-11-15T08:27:04Z][2025-05-16T07:20:40Z][2025-05-23T11:21:26Z]}
{"logTime": "1006/101251", "correlationVector":"hVm3jgd2oMOlXLQFgtUNZL","action":"Initial GetUpdates", "result":"", "context":Reason: NEW_CLIENT. cV=hVm3jgd2oMOlXLQFgtUNZL}
{"logTime": "1006/101251", "correlationVector":"hVm3jgd2oMOlXLQFgtUNZL.3","action":"GetUpdates Response", "result":"Success", "context":Received 1 update(s). cV=hVm3jgd2oMOlXLQFgtUNZL.0;server=akswtt01800004l;cloudType=Consumer;environment=Prod_southindia_prod-s01-004-apac-southindia;migrationStage=NotStarted}
{"logTime": "1006/101251", "correlationVector":"Fk0T24QYK41eu+oeq4FRgv","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=Fk0T24QYK41eu+oeq4FRgv}
{"logTime": "1006/101252", "correlationVector":"Fk0T24QYK41eu+oeq4FRgv.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southindia_prod-s01-004-apac-southindia", "migrationStage":"NotStarted", "server":"akswtt01800003h"}}
{"logTime": "1006/101252", "correlationVector":"Fk0T24QYK41eu+oeq4FRgv.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Preferences", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"99", "total":"99"}}
{"logTime": "1006/101252", "correlationVector":"Fk0T24QYK41eu+oeq4FRgv.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Passwords", "deleted":"13", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"14", "total":"14"}}
{"logTime": "1006/101252", "correlationVector":"Fk0T24QYK41eu+oeq4FRgv.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Device Info", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"47", "total":"47"}}
{"logTime": "1006/101252", "correlationVector":"Fk0T24QYK41eu+oeq4FRgv.5","action":"GetUpdates Response", "result":"Success", "context":Received 160 update(s). cV=Fk0T24QYK41eu+oeq4FRgv.0;server=akswtt01800003h;cloudType=Consumer;environment=Prod_southindia_prod-s01-004-apac-southindia;migrationStage=NotStarted}
{"logTime": "1006/101252", "correlationVector":"zLmBQX6fFzESkWY6jf61yW","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=zLmBQX6fFzESkWY6jf61yW}
{"logTime": "1006/101253", "correlationVector":"zLmBQX6fFzESkWY6jf61yW.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southindia_prod-s01-004-apac-southindia", "migrationStage":"NotStarted", "server":"akswtt01800002r"}}
{"logTime": "1006/101253", "correlationVector":"zLmBQX6fFzESkWY6jf61yW.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Bookmarks", "deleted":"2", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"22", "total":"22"}}
{"logTime": "1006/101253", "correlationVector":"zLmBQX6fFzESkWY6jf61yW.3","action":"GetUpdates Response", "result":"Success", "context":Received 22 update(s). cV=zLmBQX6fFzESkWY6jf61yW.0;server=akswtt01800002r;cloudType=Consumer;environment=Prod_southindia_prod-s01-004-apac-southindia;migrationStage=NotStarted}
{"logTime": "1006/101253", "correlationVector":"NLOuvhLy51vYZIZY3WQNPT","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=NLOuvhLy51vYZIZY3WQNPT}
{"logTime": "1006/101254", "correlationVector":"NLOuvhLy51vYZIZY3WQNPT.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southindia_prod-s01-004-apac-southindia", "migrationStage":"NotStarted", "server":"akswtt01800000x"}}
{"logTime": "1006/101254", "correlationVector":"NLOuvhLy51vYZIZY3WQNPT.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill Profiles", "deleted":"11", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"15", "total":"15"}}
{"logTime": "1006/101254", "correlationVector":"NLOuvhLy51vYZIZY3WQNPT.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill", "deleted":"35", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"55", "total":"55"}}
{"logTime": "1006/101254", "correlationVector":"NLOuvhLy51vYZIZY3WQNPT.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Extensions", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"3", "total":"3"}}
{"logTime": "1006/101254", "correlationVector":"NLOuvhLy51vYZIZY3WQNPT.5","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"170", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"170", "total":"170"}}
{"logTime": "1006/101254", "correlationVector":"NLOuvhLy51vYZIZY3WQNPT.6","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Web Apps", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"3", "total":"3"}}
{"logTime": "1006/101254", "correlationVector":"NLOuvhLy51vYZIZY3WQNPT.7","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Edge Hub App Usage", "deleted":"2", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"4", "total":"4"}}
{"logTime": "1006/101254", "correlationVector":"NLOuvhLy51vYZIZY3WQNPT.8","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=NLOuvhLy51vYZIZY3WQNPT.0;server=akswtt01800000x;cloudType=Consumer;environment=Prod_southindia_prod-s01-004-apac-southindia;migrationStage=NotStarted Some updates remain.}
{"logTime": "1006/101254", "correlationVector":"FLWIhP/i+6YPTISV7Zg6ix","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=FLWIhP/i+6YPTISV7Zg6ix}
{"logTime": "1006/101255", "correlationVector":"FLWIhP/i+6YPTISV7Zg6ix.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southindia_prod-s01-004-apac-southindia", "migrationStage":"NotStarted", "server":"akswtt01800004b"}}
{"logTime": "1006/101255", "correlationVector":"FLWIhP/i+6YPTISV7Zg6ix.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Autofill", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"11", "total":"11"}}
{"logTime": "1006/101255", "correlationVector":"FLWIhP/i+6YPTISV7Zg6ix.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"77", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"105", "total":"105"}}
{"logTime": "1006/101255", "correlationVector":"FLWIhP/i+6YPTISV7Zg6ix.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Edge Hub App Usage", "deleted":"1", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"8", "total":"8"}}
{"logTime": "1006/101255", "correlationVector":"FLWIhP/i+6YPTISV7Zg6ix.5","action":"GetUpdates Response", "result":"Success", "context":Received 124 update(s). cV=FLWIhP/i+6YPTISV7Zg6ix.0;server=akswtt01800004b;cloudType=Consumer;environment=Prod_southindia_prod-s01-004-apac-southindia;migrationStage=NotStarted}
{"logTime": "1006/101255", "correlationVector":"7PU+sLXyhI6qJz2Wivf82m","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=7PU+sLXyhI6qJz2Wivf82m}
{"logTime": "1006/101256", "correlationVector":"7PU+sLXyhI6qJz2Wivf82m.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southindia_prod-s01-004-apac-southindia", "migrationStage":"NotStarted", "server":"akswtt01800000k"}}
{"logTime": "1006/101256", "correlationVector":"7PU+sLXyhI6qJz2Wivf82m.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "1006/101256", "correlationVector":"7PU+sLXyhI6qJz2Wivf82m.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=7PU+sLXyhI6qJz2Wivf82m.0;server=akswtt01800000k;cloudType=Consumer;environment=Prod_southindia_prod-s01-004-apac-southindia;migrationStage=NotStarted Some updates remain.}
{"logTime": "1006/101256", "correlationVector":"qm0Aa2zU96UubhVEZ0XlbQ","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=qm0Aa2zU96UubhVEZ0XlbQ}
{"logTime": "1006/101258", "correlationVector":"qm0Aa2zU96UubhVEZ0XlbQ.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southindia_prod-s01-004-apac-southindia", "migrationStage":"NotStarted", "server":"akswtt01800004w"}}
{"logTime": "1006/101258", "correlationVector":"qm0Aa2zU96UubhVEZ0XlbQ.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "1006/101258", "correlationVector":"qm0Aa2zU96UubhVEZ0XlbQ.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=qm0Aa2zU96UubhVEZ0XlbQ.0;server=akswtt01800004w;cloudType=Consumer;environment=Prod_southindia_prod-s01-004-apac-southindia;migrationStage=NotStarted Some updates remain.}
{"logTime": "1006/101258", "correlationVector":"AWDX2im0wDurLISuifwASE","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=AWDX2im0wDurLISuifwASE}
{"logTime": "1006/101259", "correlationVector":"AWDX2im0wDurLISuifwASE.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southindia_prod-s01-004-apac-southindia", "migrationStage":"NotStarted", "server":"akswtt01800004k"}}
{"logTime": "1006/101259", "correlationVector":"AWDX2im0wDurLISuifwASE.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "1006/101259", "correlationVector":"AWDX2im0wDurLISuifwASE.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=AWDX2im0wDurLISuifwASE.0;server=akswtt01800004k;cloudType=Consumer;environment=Prod_southindia_prod-s01-004-apac-southindia;migrationStage=NotStarted Some updates remain.}
{"logTime": "1006/101259", "correlationVector":"yAW3w+HiUqJcT9EriFfyeu","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=yAW3w+HiUqJcT9EriFfyeu}
{"logTime": "1006/101300", "correlationVector":"yAW3w+HiUqJcT9EriFfyeu.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southindia_prod-s01-004-apac-southindia", "migrationStage":"NotStarted", "server":"akswtt01800001q"}}
{"logTime": "1006/101300", "correlationVector":"yAW3w+HiUqJcT9EriFfyeu.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"250", "total":"250"}}
{"logTime": "1006/101300", "correlationVector":"yAW3w+HiUqJcT9EriFfyeu.3","action":"GetUpdates Response", "result":"Success", "context":Received 250 update(s). cV=yAW3w+HiUqJcT9EriFfyeu.0;server=akswtt01800001q;cloudType=Consumer;environment=Prod_southindia_prod-s01-004-apac-southindia;migrationStage=NotStarted Some updates remain.}
{"logTime": "1006/101300", "correlationVector":"c/VQhqKhzwvXGS4Mvezz4d","action":"Initial GetUpdates", "result":"", "context":Reason: NEWLY_SUPPORTED_DATATYPE. cV=c/VQhqKhzwvXGS4Mvezz4d}
{"logTime": "1006/101301", "correlationVector":"c/VQhqKhzwvXGS4Mvezz4d.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southindia_prod-s01-004-apac-southindia", "migrationStage":"NotStarted", "server":"akswtt01800002t"}}
{"logTime": "1006/101301", "correlationVector":"c/VQhqKhzwvXGS4Mvezz4d.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"8", "total":"8"}}
{"logTime": "1006/101301", "correlationVector":"c/VQhqKhzwvXGS4Mvezz4d.3","action":"GetUpdates Response", "result":"Success", "context":Received 8 update(s). cV=c/VQhqKhzwvXGS4Mvezz4d.0;server=akswtt01800002t;cloudType=Consumer;environment=Prod_southindia_prod-s01-004-apac-southindia;migrationStage=NotStarted}
{"logTime": "1006/101301", "correlationVector":"gZ9Shp7z17xvdk+dPs4t0a","action":"Normal GetUpdate request", "result":"", "context":cV=gZ9Shp7z17xvdk+dPs4t0a
Nudged types: Sessions, Device Info, User Consents, History
Refresh requested types: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys}
{"logTime": "1006/101301", "correlationVector":"gZ9Shp7z17xvdk+dPs4t0a.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southindia_prod-s01-004-apac-southindia", "migrationStage":"NotStarted", "server":"akswtt01800001f"}}
{"logTime": "1006/101301", "correlationVector":"gZ9Shp7z17xvdk+dPs4t0a.2","action":"GetUpdates Response", "result":"Success", "context":Received 0 update(s). cV=gZ9Shp7z17xvdk+dPs4t0a.0;server=akswtt01800001f;cloudType=Consumer;environment=Prod_southindia_prod-s01-004-apac-southindia;migrationStage=NotStarted}
{"logTime": "1006/101301", "correlationVector":"D+78y8yF+fIaRgQFy15yPc","action":"Commit Request", "result":"", "context":Item count: 6
Contributing types: Sessions, Device Info, User Consents, History}
{"logTime": "1006/101302", "correlationVector":"D+78y8yF+fIaRgQFy15yPc.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southindia_prod-s01-004-apac-southindia", "migrationStage":"NotStarted", "server":"akswtt01800001l"}}
{"logTime": "1006/101302", "correlationVector":"D+78y8yF+fIaRgQFy15yPc.2","action":"Commit Response", "result":"Success", "context":Result: Success. cV=D+78y8yF+fIaRgQFy15yPc.0;server=akswtt01800001l;cloudType=Consumer;environment=Prod_southindia_prod-s01-004-apac-southindia;migrationStage=NotStarted}
{"logTime": "1006/101347", "correlationVector":"A44xhUuMOH8CrLN2cEnijX","action":"Commit Request", "result":"", "context":Item count: 3
Contributing types: Passwords, Sessions, History}
{"logTime": "1006/101348", "correlationVector":"A44xhUuMOH8CrLN2cEnijX.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southindia_prod-s01-004-apac-southindia", "migrationStage":"NotStarted", "server":"akswtt01800004q"}}
{"logTime": "1006/101348", "correlationVector":"A44xhUuMOH8CrLN2cEnijX.2","action":"Commit Response", "result":"Success", "context":Result: Success. cV=A44xhUuMOH8CrLN2cEnijX.0;server=akswtt01800004q;cloudType=Consumer;environment=Prod_southindia_prod-s01-004-apac-southindia;migrationStage=NotStarted}
{"logTime": "1006/101545", "correlationVector":"eIuywI0eAvUpZ8QFzqxk9V","action":"Commit Request", "result":"", "context":Item count: 4
Contributing types: Sessions, History}
{"logTime": "1006/101546", "correlationVector":"eIuywI0eAvUpZ8QFzqxk9V.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southindia_prod-s01-004-apac-southindia", "migrationStage":"NotStarted", "server":"akswtt018000053"}}
{"logTime": "1006/101546", "correlationVector":"eIuywI0eAvUpZ8QFzqxk9V.2","action":"Commit Response", "result":"Success", "context":Result: Success. cV=eIuywI0eAvUpZ8QFzqxk9V.0;server=akswtt018000053;cloudType=Consumer;environment=Prod_southindia_prod-s01-004-apac-southindia;migrationStage=NotStarted}
{"logTime": "1006/101559", "correlationVector":"laZor7WaETO9irF8kxcVtb","action":"Normal GetUpdate request", "result":"", "context":cV=laZor7WaETO9irF8kxcVtb
Notified types: Preferences, Device Info, History}
{"logTime": "1006/101600", "correlationVector":"laZor7WaETO9irF8kxcVtb.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southindia_prod-s01-004-apac-southindia", "migrationStage":"NotStarted", "server":"akswtt01800004j"}}
{"logTime": "1006/101600", "correlationVector":"laZor7WaETO9irF8kxcVtb.2","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Preferences", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "1006/101600", "correlationVector":"laZor7WaETO9irF8kxcVtb.3","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Passwords", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"1", "total":"1"}}
{"logTime": "1006/101600", "correlationVector":"laZor7WaETO9irF8kxcVtb.4","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Sessions", "deleted":"1", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"5", "total":"5"}}
{"logTime": "1006/101600", "correlationVector":"laZor7WaETO9irF8kxcVtb.5","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"Device Info", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"2", "total":"2"}}
{"logTime": "1006/101600", "correlationVector":"laZor7WaETO9irF8kxcVtb.6","action":"ProcessGetUpdates", "result":"Done", "context":{"dataType":"History", "deleted":"0", "process_failed_to_decrypt":"0", "process_pending_decryption":"0", "process_success":"4", "total":"4"}}
{"logTime": "1006/101600", "correlationVector":"laZor7WaETO9irF8kxcVtb.7","action":"GetUpdates Response", "result":"Success", "context":Received 13 update(s). cV=laZor7WaETO9irF8kxcVtb.0;server=akswtt01800004j;cloudType=Consumer;environment=Prod_southindia_prod-s01-004-apac-southindia;migrationStage=NotStarted}
{"logTime": "1006/101600", "correlationVector":"MUso8/JsJnfVta83Tx50mF","action":"DoCompareDataConsistency requsted types: ", "result":"Bookmarks, Preferences, Passwords, Extensions, Extension settings, History Delete Directives, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Wallet, Encryption Keys"}
{"logTime": "1006/101600", "correlationVector":"MUso8/JsJnfVta83Tx50mF.0","action":"CompareDataConsistency result: ", "result":"Compare result: Encryption Keys is consistent. local entities count is: 1 local entities hash is: M6XaohJhITUjooCLl6uBqwHLNzM="}
{"logTime": "1006/101600", "correlationVector":"MUso8/JsJnfVta83Tx50mF.1","action":"CompareDataConsistency result: ", "result":"Compare result: Bookmarks is consistent. local entities count is: 14 local entities hash is: mtxMUu7rsj7NLHkEzF/qU7ZVR4Y="}
{"logTime": "1006/101600", "correlationVector":"MUso8/JsJnfVta83Tx50mF.2","action":"CompareDataConsistency result: ", "result":"Compare result: Preferences is consistent. local entities count is: 99 local entities hash is: cqYV/RzR9ubSTqbXDwEjhj04+ic="}
{"logTime": "1006/101600", "correlationVector":"MUso8/JsJnfVta83Tx50mF.3","action":"CompareDataConsistency result: ", "result":"Compare result: Extensions is consistent. local entities count is: 3 local entities hash is: wSaft4ASGm/YIzzywkPBLWJSmwY="}
{"logTime": "1006/101600", "correlationVector":"MUso8/JsJnfVta83Tx50mF.4","action":"CompareDataConsistency result: ", "result":"Compare result: Extension settings is consistent. local entities count is: 0 local entities hash is: "}
{"logTime": "1006/101600", "correlationVector":"MUso8/JsJnfVta83Tx50mF.5","action":"CompareDataConsistency result: ", "result":"Compare result: History Delete Directives is consistent. local entities count is: 0 local entities hash is: "}
{"logTime": "1006/101600", "correlationVector":"MUso8/JsJnfVta83Tx50mF.6","action":"CompareDataConsistency result: ", "result":"Server did not send this Send Tab To Self local entities count is: 0 local entities hash is: "}
{"logTime": "1006/101600", "correlationVector":"MUso8/JsJnfVta83Tx50mF.7","action":"CompareDataConsistency result: ", "result":"Compare result: Web Apps is consistent. local entities count is: 3 local entities hash is: YzQXnyIxuRzMVjSs7nT7b+qCQHc="}
{"logTime": "1006/101600", "correlationVector":"MUso8/JsJnfVta83Tx50mF.8","action":"CompareDataConsistency result: ", "result":"Server did not send this History local entities count is: 0 local entities hash is: "}
{"logTime": "1006/101600", "correlationVector":"MUso8/JsJnfVta83Tx50mF.9","action":"CompareDataConsistency result: ", "result":"Compare result: Saved Tab Group is consistent. local entities count is: 0 local entities hash is: "}
{"logTime": "1006/101600", "correlationVector":"MUso8/JsJnfVta83Tx50mF.10","action":"CompareDataConsistency result: ", "result":"Server did not send this WebAuthn Credentials local entities count is: 0 local entities hash is: "}
{"logTime": "1006/101600", "correlationVector":"MUso8/JsJnfVta83Tx50mF.11","action":"CompareDataConsistency result: ", "result":"Compare result: Collection is consistent. local entities count is: 0 local entities hash is: "}
{"logTime": "1006/101600", "correlationVector":"MUso8/JsJnfVta83Tx50mF.12","action":"CompareDataConsistency result: ", "result":"Compare result: Edge E Drop is consistent. local entities count is: 0 local entities hash is: "}
{"logTime": "1006/101600", "correlationVector":"MUso8/JsJnfVta83Tx50mF.13","action":"CompareDataConsistency result: ", "result":"Compare result: Edge Wallet is consistent. local entities count is: 0 local entities hash is: "}
{"logTime": "1006/101600", "correlationVector":"MUso8/JsJnfVta83Tx50mF.14","action":"CompareDataConsistency result: ", "result":"Compare result: Passwords is consistent. local entities count is: 1 local entities hash is: bZEOfin2IZw/M5JvgvDcDwF5LjA="}
{"logTime": "1006/101600", "correlationVector":"","action":"DIAGNOSTIC_REQUEST|v1/diagnosticData/Diagnostic.SendCheckResult()|SUCCESS", "result":""}
{"logTime": "1006/101811", "correlationVector":"zVBIpB206BWZFffVATCMRV","action":"Commit Request", "result":"", "context":Item count: 3
Contributing types: Sessions, History}
{"logTime": "1006/101812", "correlationVector":"zVBIpB206BWZFffVATCMRV.1","action":"SyncServerConnectionManagerRequest", "result":"SYNC_SERVER_OK", "context":{"environment":"Prod_southindia_prod-s01-004-apac-southindia", "migrationStage":"NotStarted", "server":"akswtt018000005"}}
{"logTime": "1006/101812", "correlationVector":"zVBIpB206BWZFffVATCMRV.2","action":"Commit Response", "result":"Success", "context":Result: Success. cV=zVBIpB206BWZFffVATCMRV.0;server=akswtt018000005;cloudType=Consumer;environment=Prod_southindia_prod-s01-004-apac-southindia;migrationStage=NotStarted}
