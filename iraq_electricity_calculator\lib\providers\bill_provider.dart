import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/bill_type.dart';
import '../models/bill_calculation.dart';
import '../services/bill_calculator_service.dart';

class BillProvider with ChangeNotifier {
  BillType _selectedBillType = BillType.residential;
  int _totalKwh = 0;
  DateTime? _startDate;
  DateTime? _endDate;
  BillCalculation? _currentCalculation;
  bool _isDarkMode = false;
  List<BillCalculation> _calculationHistory = [];

  // Getters
  BillType get selectedBillType => _selectedBillType;
  int get totalKwh => _totalKwh;
  DateTime? get startDate => _startDate;
  DateTime? get endDate => _endDate;
  BillCalculation? get currentCalculation => _currentCalculation;
  bool get isDarkMode => _isDarkMode;
  List<BillCalculation> get calculationHistory => _calculationHistory;

  // Setters
  void setBillType(BillType billType) {
    _selectedBillType = billType;
    notifyListeners();
  }

  void setTotalKwh(int kwh) {
    _totalKwh = kwh;
    notifyListeners();
  }

  void setStartDate(DateTime? date) {
    _startDate = date;
    notifyListeners();
  }

  void setEndDate(DateTime? date) {
    _endDate = date;
    notifyListeners();
  }

  void toggleDarkMode() {
    _isDarkMode = !_isDarkMode;
    _saveDarkModePreference();
    notifyListeners();
  }

  void setDarkMode(bool isDark) {
    _isDarkMode = isDark;
    _saveDarkModePreference();
    notifyListeners();
  }

  // Calculate bill
  void calculateBill() {
    if (_totalKwh <= 0) return;

    _currentCalculation = BillCalculatorService.calculateBill(
      billType: _selectedBillType,
      totalKwh: _totalKwh,
      startDate: _startDate,
      endDate: _endDate,
    );

    // Add to history
    _calculationHistory.insert(0, _currentCalculation!);
    if (_calculationHistory.length > 10) {
      _calculationHistory.removeLast();
    }

    notifyListeners();
  }

  // Clear current calculation
  void clearCalculation() {
    _currentCalculation = null;
    _totalKwh = 0;
    _startDate = null;
    _endDate = null;
    notifyListeners();
  }

  // Reset all data
  void resetAll() {
    _selectedBillType = BillType.residential;
    _totalKwh = 0;
    _startDate = null;
    _endDate = null;
    _currentCalculation = null;
    notifyListeners();
  }

  // Load preferences
  Future<void> loadPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    _isDarkMode = prefs.getBool('isDarkMode') ?? false;
    notifyListeners();
  }

  // Save dark mode preference
  Future<void> _saveDarkModePreference() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isDarkMode', _isDarkMode);
  }

  // Get period text
  String get periodText {
    if (_startDate != null && _endDate != null) {
      return 'من ${_formatDate(_startDate!)} إلى ${_formatDate(_endDate!)}';
    }
    return 'فترة غير محددة';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Validation
  bool get isValidForCalculation {
    return _totalKwh > 0;
  }

  String? get validationError {
    if (_totalKwh <= 0) {
      return 'يرجى إدخال استهلاك الكهرباء';
    }
    return null;
  }

  // Get monthly average if period is set
  double? get monthlyAverage {
    if (_startDate != null && _endDate != null && _totalKwh > 0) {
      final difference = _endDate!.difference(_startDate!);
      final months = (difference.inDays / 30).round();
      if (months > 0) {
        return _totalKwh / months;
      }
    }
    return null;
  }
}
