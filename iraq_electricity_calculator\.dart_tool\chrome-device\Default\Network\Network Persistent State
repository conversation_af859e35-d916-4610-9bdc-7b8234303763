{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["GAAAABQAAABodHRwczovL2JpbmdhcGlzLmNvbQ==", false, 0], "broken_count": 1, "host": "www.bingapis.com", "port": 443, "protocol_str": "quic"}], "servers": [{"anonymization": ["HAAAABUAAABodHRwczovL21pY3Jvc29mdC5jb20AAAA=", false, 0], "server": "https://prod.rewardsplatform.microsoft.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13406809849661149", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://clients2.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13406809850088189", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3N0aXRjaC53aXRoZ29vZ2xlLmNvbQAAAA==", false, 0], "server": "https://stitch.withgoogle.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13406809850548801", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3N0aXRjaC53aXRoZ29vZ2xlLmNvbQAAAA==", false, 0], "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13406809850951125", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3N0aXRjaC53aXRoZ29vZ2xlLmNvbQAAAA==", false, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3N0aXRjaC53aXRoZ29vZ2xlLmNvbQAAAA==", true, 0], "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"anonymization": ["GAAAABQAAABodHRwczovL2JpbmdhcGlzLmNvbQ==", false, 0], "server": "https://www.bingapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "server": "https://fonts.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL29mZmljZS5jb20AAA==", false, 0], "network_stats": {"srtt": 137234}, "server": "https://substrate.office.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABodHRwczovL21pY3Jvc29mdC5jb20AAAA=", false, 0], "network_stats": {"srtt": 216962}, "server": "https://copilot.microsoft.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13406809851329737", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3N0aXRjaC53aXRoZ29vZ2xlLmNvbQAAAA==", false, 0], "network_stats": {"srtt": 64227}, "server": "https://fonts.gstatic.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13406809851243708", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3N0aXRjaC53aXRoZ29vZ2xlLmNvbQAAAA==", false, 0], "network_stats": {"srtt": 61292}, "server": "https://apis.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13406809854507499", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3N0aXRjaC53aXRoZ29vZ2xlLmNvbQAAAA==", true, 0], "network_stats": {"srtt": 85133}, "server": "https://apis.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13406809854706844", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3N0aXRjaC53aXRoZ29vZ2xlLmNvbQAAAA==", true, 0], "network_stats": {"srtt": 71822}, "server": "https://app-companion-430619.appspot.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13406809854862234", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3N0aXRjaC53aXRoZ29vZ2xlLmNvbQAAAA==", false, 0], "network_stats": {"srtt": 80936}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13406809855312748", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL3N0aXRjaC53aXRoZ29vZ2xlLmNvbQAAAA==", false, 0], "network_stats": {"srtt": 72469}, "server": "https://www.google-analytics.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13404311459004879", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAAA8AAABodHRwczovL21zbi5jb20A", false, 0], "network_stats": {"srtt": 23600}, "server": "https://img-s-msn-com.akamaized.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13404311465139391", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAAA8AAABodHRwczovL21zbi5jb20A", false, 0], "network_stats": {"srtt": 30576}, "server": "https://assets.msn.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAAA8AAABodHRwczovL21zbi5jb20A", false, 0], "network_stats": {"srtt": 118533}, "server": "https://www.msn.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", true, 0], "network_stats": {"srtt": 103145}, "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["JAAAAB0AAABodHRwczovL2dvb2dsZXVzZXJjb250ZW50LmNvbQAAAA==", false, 0], "network_stats": {"srtt": 77911}, "server": "https://clients2.googleusercontent.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 72890}, "server": "https://fonts.gstatic.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 75149}, "server": "https://googleads.g.doubleclick.net"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 80660}, "server": "https://www.youtube.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13406809869740583", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3lvdXR1YmUuY29tAA==", false, 0], "network_stats": {"srtt": 85708}, "server": "https://i.ytimg.com", "supports_spdy": true}, {"anonymization": ["LAAAACYAAABodHRwczovL2VkZ2Vhc3NldHNlcnZpY2UuYXp1cmVlZGdlLm5ldAAA", false, 0], "server": "https://edgeassetservice.azureedge.net", "supports_spdy": true}, {"anonymization": ["HAAAABUAAABodHRwczovL21pY3Jvc29mdC5jb20AAAA=", false, 0], "server": "https://edge.microsoft.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13404311443790528", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL2JpbmcuY29t", false, 0], "server": "https://www.bing.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13406809861283705", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 76476}, "server": "https://fonts.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13406809840383071", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "network_stats": {"srtt": 64410}, "server": "https://www.gstatic.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwOi8vbG9jYWxob3N0", false, 0], "server": "https://edge.microsoft.com", "supports_spdy": true}], "supports_quic": {"address": "**************", "used_quic": true}, "version": 5}, "network_qualities": {"CAISABiAgICA+P////8B": "4G", "CAYSABiAgICA+P////8B": "Offline"}}}