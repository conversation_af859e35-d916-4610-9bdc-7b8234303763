enum BillType {
  residential,
  commercial,
}

extension BillTypeExtension on BillType {
  String get name {
    switch (this) {
      case BillType.residential:
        return 'منزلي';
      case BillType.commercial:
        return 'تجاري';
    }
  }
  
  String get englishName {
    switch (this) {
      case BillType.residential:
        return 'Residential';
      case BillType.commercial:
        return 'Commercial';
    }
  }
  
  double get subscriptionFee {
    switch (this) {
      case BillType.residential:
        return 2000.0;
      case BillType.commercial:
        return 5000.0;
    }
  }
}
