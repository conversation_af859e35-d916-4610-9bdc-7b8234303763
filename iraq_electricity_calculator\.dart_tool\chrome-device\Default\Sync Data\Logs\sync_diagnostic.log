2025-10-06 09:50:39.286: [INFO][Sync] Reset engine, reason: 8
2025-10-06 09:50:39.286: [INFO][Sync] Stopped: Bookmarks
2025-10-06 09:50:39.286: [INFO][Sync] Stopped: Preferences
2025-10-06 09:50:39.286: [INFO][Sync] Stopped: Passwords
2025-10-06 09:50:39.286: [INFO][Sync] Stopped: Autofill Profiles
2025-10-06 09:50:39.286: [INFO][Sync] Stopped: Autofill
2025-10-06 09:50:39.286: [INFO][Sync] Stopped: Extensions
2025-10-06 09:50:39.286: [INFO][Sync] Stopped: Sessions
2025-10-06 09:50:39.286: [INFO][Sync] Stopped: Extension settings
2025-10-06 09:50:39.286: [INFO][Sync] Stopped: History Delete Directives
2025-10-06 09:50:39.286: [INFO][Sync] Stopped: Device Info
2025-10-06 09:50:39.286: [INFO][Sync] Stopped: User Consents
2025-10-06 09:50:39.286: [INFO][Sync] Stopped: Send Tab To Self
2025-10-06 09:50:39.286: [INFO][Sync] Stopped: Web Apps
2025-10-06 09:50:39.286: [INFO][Sync] Stopped: History
2025-10-06 09:50:39.286: [INFO][Sync] Stopped: Saved Tab Group
2025-10-06 09:50:39.286: [INFO][Sync] Stopped: WebAuthn Credentials
2025-10-06 09:50:39.286: [INFO][Sync] Stopped: Collection
2025-10-06 09:50:39.286: [INFO][Sync] Stopped: Edge E Drop
2025-10-06 09:50:39.286: [INFO][Sync] Stopped: Edge Hub App Usage
2025-10-06 09:50:39.286: [INFO][Sync] Stopped: Edge Wallet
2025-10-06 09:50:39.286: [INFO][Sync] SyncState after authenticated was: NotSignedIn
2025-10-06 09:50:39.624: [INFO][Sync] Reset engine, reason: 8
2025-10-06 09:50:39.624: [INFO][Sync] Stopped: Bookmarks
2025-10-06 09:50:39.624: [INFO][Sync] Stopped: Preferences
2025-10-06 09:50:39.624: [INFO][Sync] Stopped: Passwords
2025-10-06 09:50:39.624: [INFO][Sync] Stopped: Autofill Profiles
2025-10-06 09:50:39.624: [INFO][Sync] Stopped: Autofill
2025-10-06 09:50:39.624: [INFO][Sync] Stopped: Extensions
2025-10-06 09:50:39.624: [INFO][Sync] Stopped: Sessions
2025-10-06 09:50:39.624: [INFO][Sync] Stopped: Extension settings
2025-10-06 09:50:39.624: [INFO][Sync] Stopped: History Delete Directives
2025-10-06 09:50:39.624: [INFO][Sync] Stopped: Device Info
2025-10-06 09:50:39.624: [INFO][Sync] Stopped: User Consents
2025-10-06 09:50:39.624: [INFO][Sync] Stopped: Send Tab To Self
2025-10-06 09:50:39.624: [INFO][Sync] Stopped: Web Apps
2025-10-06 09:50:39.624: [INFO][Sync] Stopped: History
2025-10-06 09:50:39.624: [INFO][Sync] Stopped: Saved Tab Group
2025-10-06 09:50:39.624: [INFO][Sync] Stopped: WebAuthn Credentials
2025-10-06 09:50:39.624: [INFO][Sync] Stopped: Collection
2025-10-06 09:50:39.624: [INFO][Sync] Stopped: Edge E Drop
2025-10-06 09:50:39.624: [INFO][Sync] Stopped: Edge Hub App Usage
2025-10-06 09:50:39.624: [INFO][Sync] Stopped: Edge Wallet
2025-10-06 09:50:39.823: [INFO][Sync] Try to start sync engine
2025-10-06 09:50:39.824: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Encryption Keys
2025-10-06 09:50:39.824: [INFO][SyncEngineBackend::LoadAndConnectNigoriController] Load and connect Nigori controller
2025-10-06 09:50:39.824: [INFO][SyncEngineBackend::DoInitialize] Control Types added: Encryption Keys
2025-10-06 09:50:39.824: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Encryption Keys with reason: 3
2025-10-06 09:50:39.824: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Configure job was blocked
2025-10-06 09:50:43.509: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-10-06 09:50:43.509: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-10-06 09:50:43.509: [INFO][Sync] Credentials changed for: EdgeSyncKeyDataScope
2025-10-06 09:50:43.509: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-10-06 09:50:43.601: [INFO][SyncAuthManager::SetLastAuthError] Current auth error: None
2025-10-06 09:50:43.601: [INFO][SyncAuthManager::EdgeLogTokenErrorState] Token error with: None for account type: MSA
2025-10-06 09:50:43.601: [INFO][Sync] Credentials changed for: EdgeSyncScopeNew
2025-10-06 09:50:43.603: [INFO][SyncEngineBackend::DoUpdateCredentials] Update credentials
2025-10-06 09:50:43.607: [INFO][SyncEngineBackend::DoUpdateKeyDataCredentials] Update key data credentials
2025-10-06 09:50:43.607: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Encryption Keys
2025-10-06 09:50:45.118: [INFO][Sync] Started DataTypeManager configuration, reason: 4
2025-10-06 09:50:45.136: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys, reason: 3, current state: 0
2025-10-06 09:50:45.136: [WARN][Sync] Crypto error data types: Passwords, Autofill Profiles, Autofill
2025-10-06 09:50:45.165: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys; Allow fail: Passwords, Autofill Profiles, Autofill
2025-10-06 09:50:45.165: [INFO][Sync] Loading: Bookmarks
2025-10-06 09:50:45.165: [INFO][Sync] Loading: Preferences
2025-10-06 09:50:45.165: [INFO][Sync] Loading: Extensions
2025-10-06 09:50:45.183: [INFO][Sync] Loading: Sessions
2025-10-06 09:50:45.190: [INFO][Sync] Loading: Extension settings
2025-10-06 09:50:45.190: [INFO][Sync] Loading: History Delete Directives
2025-10-06 09:50:45.193: [INFO][Sync] Loading: Device Info
2025-10-06 09:50:45.194: [INFO][Sync] Loading: User Consents
2025-10-06 09:50:45.194: [INFO][Sync] Loading: Send Tab To Self
2025-10-06 09:50:45.194: [INFO][Sync] Loading: Web Apps
2025-10-06 09:50:45.194: [INFO][Sync] Loading: History
2025-10-06 09:50:45.194: [INFO][Sync] Loading: Saved Tab Group
2025-10-06 09:50:45.194: [INFO][Sync] Loading: WebAuthn Credentials
2025-10-06 09:50:45.194: [INFO][Sync] Loading: Collection
2025-10-06 09:50:45.194: [INFO][Sync] Loading: Edge E Drop
2025-10-06 09:50:45.194: [INFO][Sync] Loading: Edge Hub App Usage
2025-10-06 09:50:45.194: [INFO][Sync] Loading: Edge Wallet
2025-10-06 09:50:45.270: [INFO][Sync] All data types are ready for configure.
2025-10-06 09:50:45.931: [INFO][Sync] Started DataTypeManager configuration, reason: 5
2025-10-06 09:50:45.931: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys, reason: 5, current state: 1
2025-10-06 09:50:45.936: [INFO][SyncEngineBackend::DoStartConfiguration] Start configuration
2025-10-06 09:50:45.939: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Bookmarks
2025-10-06 09:50:45.939: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Preferences
2025-10-06 09:50:45.939: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Extensions
2025-10-06 09:50:45.939: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Sessions
2025-10-06 09:50:45.939: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Extension settings
2025-10-06 09:50:45.939: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for History Delete Directives
2025-10-06 09:50:45.939: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Device Info
2025-10-06 09:50:45.939: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Send Tab To Self
2025-10-06 09:50:45.939: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Web Apps
2025-10-06 09:50:45.939: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for History
2025-10-06 09:50:45.939: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Saved Tab Group
2025-10-06 09:50:45.939: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for WebAuthn Credentials
2025-10-06 09:50:45.939: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Collection
2025-10-06 09:50:45.939: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Edge E Drop
2025-10-06 09:50:45.939: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Edge Hub App Usage
2025-10-06 09:50:45.939: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Edge Wallet
2025-10-06 09:50:45.939: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 3
2025-10-06 09:50:45.939: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-10-06 09:50:45.940: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-10-06 09:50:45.940: [WARN][Sync]     Reconfigure requested while configuration ongoing.
2025-10-06 09:50:45.940: [INFO][Sync] Configuring for: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys, reason: 5, current state: 2
2025-10-06 09:50:45.940: [INFO][Sync] Configure, desired: Bookmarks, Preferences, Passwords, Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Device Info, User Consents, Send Tab To Self, Web Apps, History, Saved Tab Group, WebAuthn Credentials, Collection, Edge E Drop, Edge Hub App Usage, Edge Wallet, Encryption Keys; Allow fail: 
2025-10-06 09:50:45.940: [INFO][Sync] Loading: Passwords
2025-10-06 09:50:45.940: [INFO][Sync] Loading: Autofill Profiles
2025-10-06 09:50:45.941: [INFO][Sync] Loading: Autofill
2025-10-06 09:50:45.941: [INFO][Sync] All data types are ready for configure.
2025-10-06 09:50:45.941: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Passwords
2025-10-06 09:50:45.941: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Autofill Profiles
2025-10-06 09:50:45.941: [INFO][SyncManagerImpl::NudgeForInitialDownload] Initial download nudge for Autofill
2025-10-06 09:50:45.941: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download:  with reason: 5
2025-10-06 09:50:45.941: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: 
2025-10-06 09:50:45.941: [INFO][Sync] ConfigurationDone, failed: , succeeded: , remaining count: 4
2025-10-06 09:50:45.941: [INFO][Sync] Prepare to configure types: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys
2025-10-06 09:50:45.941: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys with reason: 5
2025-10-06 09:50:45.941: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys
2025-10-06 09:50:46.814: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys
2025-10-06 09:50:46.859: [INFO][Sync] ConfigurationDone, failed: , succeeded: Preferences, Passwords, Device Info, Send Tab To Self, Edge E Drop, Encryption Keys, remaining count: 3
2025-10-06 09:50:46.860: [INFO][Sync] Prepare to configure types: Bookmarks, Encryption Keys
2025-10-06 09:50:46.860: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Bookmarks, Encryption Keys with reason: 5
2025-10-06 09:50:46.860: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Bookmarks, Encryption Keys
2025-10-06 09:50:47.296: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: Bookmarks, Encryption Keys
2025-10-06 09:50:47.307: [INFO][Sync] ConfigurationDone, failed: , succeeded: Bookmarks, Encryption Keys, remaining count: 2
2025-10-06 09:50:47.307: [INFO][Sync] Prepare to configure types: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, WebAuthn Credentials, Collection, Edge Hub App Usage, Edge Wallet, Encryption Keys
2025-10-06 09:50:47.307: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, WebAuthn Credentials, Collection, Edge Hub App Usage, Edge Wallet, Encryption Keys with reason: 5
2025-10-06 09:50:47.308: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, WebAuthn Credentials, Collection, Edge Hub App Usage, Edge Wallet, Encryption Keys
2025-10-06 09:50:49.319: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, WebAuthn Credentials, Collection, Edge Hub App Usage, Edge Wallet, Encryption Keys
2025-10-06 09:50:49.321: [INFO][Sync] ConfigurationDone, failed: , succeeded: Autofill Profiles, Autofill, Extensions, Sessions, Extension settings, History Delete Directives, Web Apps, Saved Tab Group, WebAuthn Credentials, Collection, Edge Hub App Usage, Edge Wallet, Encryption Keys, remaining count: 1
2025-10-06 09:50:49.324: [INFO][Sync] Prepare to configure types: History, Encryption Keys
2025-10-06 09:50:49.324: [INFO][SyncManagerImpl::ConfigureSyncer] Types to download: History, Encryption Keys with reason: 5
2025-10-06 09:50:49.324: [INFO][SyncSchedulerImpl::DoConfigurationSyncCycleJob] Blocked types:  and types to download: History, Encryption Keys
2025-10-06 09:50:58.300: [INFO][SyncEngineBackend::DoFinishConfigureDataTypes] Failed to download types:  and succeeded types: History, Encryption Keys
2025-10-06 09:50:58.303: [INFO][Sync] ConfigurationDone, failed: , succeeded: History, Encryption Keys, remaining count: 0
2025-10-06 09:50:58.308: [INFO][Sync]     Configuration completed, state: 7
2025-10-06 09:50:58.308: [INFO][Sync] Configured DataTypeManager: Ok
2025-10-06 09:50:58.314: [INFO][SyncEngineBackend::DoStartSyncing] Start syncing
2025-10-06 10:04:47.150: [INFO][SyncSchedulerImpl::OnSyncNetworkError] Syncer error: Network error (ERR_INTERNET_DISCONNECTED)
2025-10-06 10:04:47.151: [INFO][Commit::ReportFullCommitFailure] Commit Sessions, History failed with error: NetworkError
2025-10-06 10:04:47.151: [INFO][SyncSchedulerImpl::DoNudgeSyncCycleJob] Syncer error: Network error (ERR_INTERNET_DISCONNECTED)
2025-10-06 10:04:47.155: [WARN][Sync] NetworkError: Network error (ERR_INTERNET_DISCONNECTED)
2025-10-06 10:04:48.687: [INFO][SyncSchedulerImpl::OnSyncNetworkError] Syncer error: Network error (ERR_INTERNET_DISCONNECTED)
2025-10-06 10:04:48.687: [INFO][Commit::ReportFullCommitFailure] Commit Sessions, History failed with error: NetworkError
2025-10-06 10:04:48.688: [INFO][SyncSchedulerImpl::DoNudgeSyncCycleJob] Syncer error: Network error (ERR_INTERNET_DISCONNECTED)
2025-10-06 10:04:48.690: [WARN][Sync] NetworkError: Network error (ERR_INTERNET_DISCONNECTED)
2025-10-06 10:04:50.952: [INFO][SyncSchedulerImpl::OnSyncNetworkError] Syncer error: Network error (ERR_INTERNET_DISCONNECTED)
2025-10-06 10:04:50.953: [INFO][Commit::ReportFullCommitFailure] Commit Sessions, History failed with error: NetworkError
2025-10-06 10:04:50.953: [INFO][SyncSchedulerImpl::DoNudgeSyncCycleJob] Syncer error: Network error (ERR_INTERNET_DISCONNECTED)
2025-10-06 10:04:50.958: [WARN][Sync] NetworkError: Network error (ERR_INTERNET_DISCONNECTED)
2025-10-06 10:04:56.586: [INFO][SyncSchedulerImpl::OnSyncNetworkError] Syncer error: Network error (ERR_INTERNET_DISCONNECTED)
2025-10-06 10:04:56.587: [INFO][Commit::ReportFullCommitFailure] Commit Sessions, History failed with error: NetworkError
2025-10-06 10:04:56.587: [INFO][SyncSchedulerImpl::DoNudgeSyncCycleJob] Syncer error: Network error (ERR_INTERNET_DISCONNECTED)
2025-10-06 10:04:56.588: [WARN][Sync] NetworkError: Network error (ERR_INTERNET_DISCONNECTED)
2025-10-06 10:05:10.661: [INFO][SyncSchedulerImpl::OnSyncNetworkError] Syncer error: Network error (ERR_INTERNET_DISCONNECTED)
2025-10-06 10:05:10.661: [INFO][Commit::ReportFullCommitFailure] Commit Sessions, History failed with error: NetworkError
2025-10-06 10:05:10.661: [INFO][SyncSchedulerImpl::DoNudgeSyncCycleJob] Syncer error: Network error (ERR_INTERNET_DISCONNECTED)
2025-10-06 10:05:10.665: [WARN][Sync] NetworkError: Network error (ERR_INTERNET_DISCONNECTED)
2025-10-06 10:05:26.753: [INFO][SyncSchedulerImpl::OnSyncNetworkError] Syncer error: Network error (ERR_NAME_NOT_RESOLVED)
2025-10-06 10:05:26.753: [INFO][Commit::ReportFullCommitFailure] Commit Sessions, History failed with error: NetworkError
2025-10-06 10:05:26.754: [INFO][SyncSchedulerImpl::DoNudgeSyncCycleJob] Syncer error: Network error (ERR_NAME_NOT_RESOLVED)
2025-10-06 10:05:26.756: [WARN][Sync] NetworkError: Network error (ERR_NAME_NOT_RESOLVED)
2025-10-06 10:05:29.662: [INFO][SyncSchedulerImpl::OnSyncNetworkError] Syncer error: Network error (ERR_NAME_NOT_RESOLVED)
2025-10-06 10:05:29.662: [INFO][Commit::ReportFullCommitFailure] Commit Sessions, History failed with error: NetworkError
2025-10-06 10:05:29.663: [INFO][SyncSchedulerImpl::DoNudgeSyncCycleJob] Syncer error: Network error (ERR_NAME_NOT_RESOLVED)
2025-10-06 10:05:29.664: [WARN][Sync] NetworkError: Network error (ERR_NAME_NOT_RESOLVED)
2025-10-06 10:05:31.797: [INFO][SyncSchedulerImpl::OnSyncNetworkError] Syncer error: Network error (ERR_NAME_NOT_RESOLVED)
2025-10-06 10:05:31.797: [INFO][Commit::ReportFullCommitFailure] Commit Sessions, History failed with error: NetworkError
2025-10-06 10:05:31.797: [INFO][SyncSchedulerImpl::DoNudgeSyncCycleJob] Syncer error: Network error (ERR_NAME_NOT_RESOLVED)
2025-10-06 10:05:31.800: [WARN][Sync] NetworkError: Network error (ERR_NAME_NOT_RESOLVED)
2025-10-06 10:05:46.166: [INFO][SyncSchedulerImpl::OnSyncNetworkError] Syncer error: Network error (ERR_NAME_NOT_RESOLVED)
2025-10-06 10:05:46.166: [INFO][Commit::ReportFullCommitFailure] Commit Sessions, History failed with error: NetworkError
2025-10-06 10:05:46.166: [INFO][SyncSchedulerImpl::DoNudgeSyncCycleJob] Syncer error: Network error (ERR_NAME_NOT_RESOLVED)
2025-10-06 10:05:46.168: [WARN][Sync] NetworkError: Network error (ERR_NAME_NOT_RESOLVED)
2025-10-06 10:06:04.533: [INFO][SyncSchedulerImpl::OnSyncNetworkError] Syncer error: Network error (ERR_NAME_NOT_RESOLVED)
2025-10-06 10:06:04.533: [INFO][Commit::ReportFullCommitFailure] Commit Sessions, History failed with error: NetworkError
2025-10-06 10:06:04.534: [INFO][SyncSchedulerImpl::DoNudgeSyncCycleJob] Syncer error: Network error (ERR_NAME_NOT_RESOLVED)
2025-10-06 10:06:04.538: [WARN][Sync] NetworkError: Network error (ERR_NAME_NOT_RESOLVED)
2025-10-06 10:06:07.180: [INFO][SyncSchedulerImpl::OnSyncNetworkError] Syncer error: Network error (ERR_NAME_NOT_RESOLVED)
2025-10-06 10:06:07.180: [INFO][Commit::ReportFullCommitFailure] Commit Sessions, History failed with error: NetworkError
2025-10-06 10:06:07.180: [INFO][SyncSchedulerImpl::DoNudgeSyncCycleJob] Syncer error: Network error (ERR_NAME_NOT_RESOLVED)
2025-10-06 10:06:07.184: [WARN][Sync] NetworkError: Network error (ERR_NAME_NOT_RESOLVED)
2025-10-06 10:06:10.190: [INFO][SyncSchedulerImpl::OnSyncNetworkError] Syncer error: Network error (ERR_NAME_NOT_RESOLVED)
2025-10-06 10:06:10.190: [INFO][Commit::ReportFullCommitFailure] Commit Sessions, History failed with error: NetworkError
2025-10-06 10:06:10.190: [INFO][SyncSchedulerImpl::DoNudgeSyncCycleJob] Syncer error: Network error (ERR_NAME_NOT_RESOLVED)
2025-10-06 10:06:10.193: [WARN][Sync] NetworkError: Network error (ERR_NAME_NOT_RESOLVED)
2025-10-06 10:07:44.422: [INFO][Sync] Reset engine, reason: 0
2025-10-06 10:07:44.422: [INFO][Sync] Reset engine with reason: 0
