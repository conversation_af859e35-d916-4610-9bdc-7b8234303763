import 'package:flutter/material.dart';
import '../constants/app_colors.dart';

class PricingTier {
  final int minKwh;
  final int? maxKwh;
  final double pricePerKwh;
  final String name;
  final Color color;

  const PricingTier({
    required this.minKwh,
    this.maxKwh,
    required this.pricePerKwh,
    required this.name,
    required this.color,
  });

  bool isInRange(int kwh) {
    if (maxKwh == null) {
      return kwh >= minKwh;
    }
    return kwh >= minKwh && kwh <= maxKwh!;
  }

  String get rangeText {
    if (maxKwh == null) {
      return 'أكثر من $minKwh كيلو واط ساعة';
    }
    return '$minKwh - $maxKwh كيلو واط ساعة';
  }

  String get priceText {
    return '${pricePerKwh.toInt()} دينار/كيلو واط ساعة';
  }
}

class PricingTiers {
  // Residential Pricing Tiers
  static const List<PricingTier> residential = [
    PricingTier(
      minKwh: 1,
      maxKwh: 3000,
      pricePerKwh: 10.0,
      name: 'الشريحة الأولى',
      color: AppColors.tier1Color,
    ),
    PricingTier(
      minKwh: 3001,
      maxKwh: 6000,
      pricePerKwh: 35.0,
      name: 'الشريحة الثانية',
      color: AppColors.tier2Color,
    ),
    PricingTier(
      minKwh: 6001,
      maxKwh: 8000,
      pricePerKwh: 80.0,
      name: 'الشريحة الثالثة',
      color: AppColors.tier3Color,
    ),
    PricingTier(
      minKwh: 8001,
      maxKwh: null,
      pricePerKwh: 120.0,
      name: 'الشريحة الرابعة',
      color: AppColors.tier4Color,
    ),
  ];

  // Commercial Pricing Tiers
  static const List<PricingTier> commercial = [
    PricingTier(
      minKwh: 1,
      maxKwh: 2000,
      pricePerKwh: 60.0,
      name: 'الشريحة الأولى',
      color: AppColors.tier1Color,
    ),
    PricingTier(
      minKwh: 2001,
      maxKwh: 4000,
      pricePerKwh: 80.0,
      name: 'الشريحة الثانية',
      color: AppColors.tier2Color,
    ),
    PricingTier(
      minKwh: 4001,
      maxKwh: null,
      pricePerKwh: 120.0,
      name: 'الشريحة الثالثة',
      color: AppColors.tier3Color,
    ),
  ];
}
