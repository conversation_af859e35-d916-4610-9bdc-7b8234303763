# This file tracks properties of this Flutter project.
# Used by Flutter tool to assess capabilities and perform upgrades etc.
#
# This file should be version controlled and should not be manually edited.

version:
  revision: "d693b4b9dbac2acd4477aea4555ca6dcbea44ba2"
  channel: "stable"

project_type: app

# Tracks metadata for the flutter migrate command
migration:
  platforms:
    - platform: root
      create_revision: d693b4b9dbac2acd4477aea4555ca6dcbea44ba2
      base_revision: d693b4b9dbac2acd4477aea4555ca6dcbea44ba2
    - platform: android
      create_revision: d693b4b9dbac2acd4477aea4555ca6dcbea44ba2
      base_revision: d693b4b9dbac2acd4477aea4555ca6dcbea44ba2
    - platform: ios
      create_revision: d693b4b9dbac2acd4477aea4555ca6dcbea44ba2
      base_revision: d693b4b9dbac2acd4477aea4555ca6dcbea44ba2
    - platform: linux
      create_revision: d693b4b9dbac2acd4477aea4555ca6dcbea44ba2
      base_revision: d693b4b9dbac2acd4477aea4555ca6dcbea44ba2
    - platform: macos
      create_revision: d693b4b9dbac2acd4477aea4555ca6dcbea44ba2
      base_revision: d693b4b9dbac2acd4477aea4555ca6dcbea44ba2
    - platform: web
      create_revision: d693b4b9dbac2acd4477aea4555ca6dcbea44ba2
      base_revision: d693b4b9dbac2acd4477aea4555ca6dcbea44ba2
    - platform: windows
      create_revision: d693b4b9dbac2acd4477aea4555ca6dcbea44ba2
      base_revision: d693b4b9dbac2acd4477aea4555ca6dcbea44ba2

  # User provided section

  # List of Local paths (relative to this file) that should be
  # ignored by the migrate tool.
  #
  # Files that are not part of the templates will be ignored by default.
  unmanaged_files:
    - 'lib/main.dart'
    - 'ios/Runner.xcodeproj/project.pbxproj'
