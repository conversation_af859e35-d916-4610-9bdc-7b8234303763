import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'providers/bill_provider.dart';
import 'screens/home_screen.dart';
import 'constants/app_themes_simple.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(const IraqElectricityCalculatorApp());
}

class IraqElectricityCalculatorApp extends StatelessWidget {
  const IraqElectricityCalculatorApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => BillProvider()..loadPreferences(),
      child: Consumer<BillProvider>(
        builder: (context, billProvider, child) {
          return MaterialApp(
            title: 'حاسبة فواتير الكهرباء العراقية',
            debugShowCheckedModeBanner: false,

            // Themes
            theme: AppThemes.lightTheme,
            darkTheme: AppThemes.darkTheme,
            themeMode: billProvider.isDarkMode
                ? ThemeMode.dark
                : ThemeMode.light,

            // Localization
            locale: const Locale('ar', 'IQ'),
            supportedLocales: const [Locale('ar', 'IQ'), Locale('en', 'US')],
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],

            // Home screen
            home: const HomeScreen(),
          );
        },
      ),
    );
  }
}
