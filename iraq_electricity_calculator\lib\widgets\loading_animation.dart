import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../constants/app_colors.dart';

class LoadingAnimation extends StatelessWidget {
  final String? message;
  final double size;
  final Color? color;

  const LoadingAnimation({
    super.key,
    this.message,
    this.size = 50.0,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: Stack(
            children: [
              // Outer ring
              Container(
                width: size,
                height: size,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: (color ?? AppColors.primaryColor).withOpacity(0.2),
                    width: 3,
                  ),
                ),
              ),
              // Inner spinning ring
              Container(
                width: size,
                height: size,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.transparent,
                    width: 3,
                  ),
                ),
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    color ?? AppColors.primaryColor,
                  ),
                ),
              ).animate(onPlay: (controller) => controller.repeat())
                  .rotate(duration: 1000.ms),
              // Center icon
              Center(
                child: Icon(
                  Icons.electric_bolt,
                  color: color ?? AppColors.primaryColor,
                  size: size * 0.4,
                ).animate(onPlay: (controller) => controller.repeat())
                    .scale(
                      begin: const Offset(0.8, 0.8),
                      end: const Offset(1.2, 1.2),
                      duration: 1000.ms,
                    )
                    .then()
                    .scale(
                      begin: const Offset(1.2, 1.2),
                      end: const Offset(0.8, 0.8),
                      duration: 1000.ms,
                    ),
              ),
            ],
          ),
        ),
        if (message != null) ...[
          const SizedBox(height: 16),
          Text(
            message!,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: color ?? AppColors.primaryColor,
            ),
            textAlign: TextAlign.center,
          ).animate()
              .fadeIn(delay: 300.ms, duration: 600.ms)
              .slideY(begin: 0.3, end: 0),
        ],
      ],
    );
  }
}

class PulsingDot extends StatelessWidget {
  final Color color;
  final double size;
  final Duration duration;

  const PulsingDot({
    super.key,
    this.color = AppColors.primaryColor,
    this.size = 8.0,
    this.duration = const Duration(milliseconds: 1000),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    ).animate(onPlay: (controller) => controller.repeat())
        .scale(
          begin: const Offset(0.5, 0.5),
          end: const Offset(1.5, 1.5),
          duration: duration,
        )
        .fadeOut(duration: duration);
  }
}

class WaveAnimation extends StatelessWidget {
  final Color color;
  final double height;
  final int waveCount;

  const WaveAnimation({
    super.key,
    this.color = AppColors.primaryColor,
    this.height = 40.0,
    this.waveCount = 5,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(waveCount, (index) {
        return Container(
          width: 4,
          height: height,
          margin: const EdgeInsets.symmetric(horizontal: 2),
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ).animate(onPlay: (controller) => controller.repeat())
            .scaleY(
              begin: 0.3,
              end: 1.0,
              duration: Duration(milliseconds: 600 + (index * 100)),
            )
            .then()
            .scaleY(
              begin: 1.0,
              end: 0.3,
              duration: Duration(milliseconds: 600 + (index * 100)),
            );
      }),
    );
  }
}
