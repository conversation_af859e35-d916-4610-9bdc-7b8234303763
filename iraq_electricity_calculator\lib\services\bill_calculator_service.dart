import '../models/bill_type.dart';
import '../models/bill_calculation.dart';
import '../models/pricing_tier.dart';

class BillCalculatorService {
  static BillCalculation calculateBill({
    required BillType billType,
    required int totalKwh,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    // حساب عدد الأشهر في الفترة
    int periodMonths = 1;
    if (startDate != null && endDate != null) {
      final difference = endDate.difference(startDate);
      periodMonths = (difference.inDays / 30).round();
      if (periodMonths < 1) periodMonths = 1;
    }

    // إذا كانت الفترة أكثر من شهرين، قسم على فترات كل شهرين
    if (periodMonths > 2) {
      return _calculateForLongPeriod(
        billType: billType,
        totalKwh: totalKwh,
        periodMonths: periodMonths,
        startDate: startDate,
        endDate: endDate,
      );
    }

    // الحساب العادي للفترات القصيرة (شهر أو شهرين)
    return _calculateNormalBill(
      billType: billType,
      totalKwh: totalKwh,
      startDate: startDate,
      endDate: endDate,
    );
  }

  static BillCalculation _calculateNormalBill({
    required BillType billType,
    required int totalKwh,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    final List<PricingTier> tiers = billType == BillType.residential
        ? PricingTiers.residential
        : PricingTiers.commercial;

    final List<TierCalculation> tierCalculations = [];
    double totalElectricityAmount = 0.0;
    int remainingKwh = totalKwh;

    for (final tier in tiers) {
      if (remainingKwh <= 0) break;

      int kwhForThisTier;
      if (tier.maxKwh == null) {
        // Last tier - use all remaining kwh
        kwhForThisTier = remainingKwh;
      } else {
        // Calculate kwh for this tier
        int tierCapacity = tier.maxKwh! - tier.minKwh + 1;
        kwhForThisTier = remainingKwh > tierCapacity
            ? tierCapacity
            : remainingKwh;
      }

      if (kwhForThisTier > 0) {
        double amount = kwhForThisTier * tier.pricePerKwh;
        tierCalculations.add(
          TierCalculation(tier: tier, kwhUsed: kwhForThisTier, amount: amount),
        );
        totalElectricityAmount += amount;
        remainingKwh -= kwhForThisTier;
      }
    }

    final double subscriptionFee = billType.subscriptionFee;
    final double totalAmount = totalElectricityAmount + subscriptionFee;

    return BillCalculation(
      billType: billType,
      totalKwh: totalKwh,
      subscriptionFee: subscriptionFee,
      tierCalculations: tierCalculations,
      totalElectricityAmount: totalElectricityAmount,
      totalAmount: totalAmount,
      calculationDate: DateTime.now(),
      startDate: startDate,
      endDate: endDate,
    );
  }

  static BillCalculation _calculateForLongPeriod({
    required BillType billType,
    required int totalKwh,
    required int periodMonths,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    // حساب عدد الفترات (كل فترة = شهرين)
    final int twoMonthPeriods = (periodMonths / 2).ceil();

    // توزيع الاستهلاك على الفترات
    final int kwhPerPeriod = (totalKwh / twoMonthPeriods).round();

    final List<TierCalculation> allTierCalculations = [];
    double totalElectricityAmount = 0.0;
    double totalSubscriptionFees = 0.0;

    // حساب كل فترة على حدة
    for (int i = 0; i < twoMonthPeriods; i++) {
      int currentPeriodKwh = kwhPerPeriod;

      // في الفترة الأخيرة، أضف أي وحدات متبقية
      if (i == twoMonthPeriods - 1) {
        currentPeriodKwh = totalKwh - (kwhPerPeriod * (twoMonthPeriods - 1));
      }

      final periodCalculation = _calculateNormalBill(
        billType: billType,
        totalKwh: currentPeriodKwh,
        startDate: null,
        endDate: null,
      );

      // إضافة الشرائح مع تسمية الفترة
      for (final tierCalc in periodCalculation.tierCalculations) {
        final periodTier = TierCalculation(
          tier: PricingTier(
            minKwh: tierCalc.tier.minKwh,
            maxKwh: tierCalc.tier.maxKwh,
            pricePerKwh: tierCalc.tier.pricePerKwh,
            name: '${tierCalc.tier.name} - الفترة ${i + 1}',
            color: tierCalc.tier.color,
          ),
          kwhUsed: tierCalc.kwhUsed,
          amount: tierCalc.amount,
        );
        allTierCalculations.add(periodTier);
      }

      totalElectricityAmount += periodCalculation.totalElectricityAmount;
      totalSubscriptionFees += periodCalculation.subscriptionFee;
    }

    final double totalAmount = totalElectricityAmount + totalSubscriptionFees;

    return BillCalculation(
      billType: billType,
      totalKwh: totalKwh,
      subscriptionFee: totalSubscriptionFees,
      tierCalculations: allTierCalculations,
      totalElectricityAmount: totalElectricityAmount,
      totalAmount: totalAmount,
      calculationDate: DateTime.now(),
      startDate: startDate,
      endDate: endDate,
    );
  }

  static List<PricingTier> getTiersForBillType(BillType billType) {
    return billType == BillType.residential
        ? PricingTiers.residential
        : PricingTiers.commercial;
  }

  static String formatCurrency(double amount) {
    return '${amount.toInt().toString().replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]},')} دينار';
  }

  static double calculateMonthlyAverage(int totalKwh, int months) {
    if (months <= 0) return 0.0;
    return totalKwh / months;
  }

  static Map<String, dynamic> getBillSummary(BillCalculation calculation) {
    return {
      'billType': calculation.billTypeText,
      'totalKwh': calculation.totalKwhText,
      'subscriptionFee': formatCurrency(calculation.subscriptionFee),
      'electricityAmount': formatCurrency(calculation.totalElectricityAmount),
      'totalAmount': formatCurrency(calculation.totalAmount),
      'tierBreakdown': calculation.tierCalculations
          .map(
            (tier) => {
              'tierName': tier.tier.name,
              'range': tier.tier.rangeText,
              'kwhUsed': '${tier.kwhUsed} كيلو واط ساعة',
              'pricePerKwh': tier.tier.priceText,
              'amount': formatCurrency(tier.amount),
              'color': tier.tier.color.value,
            },
          )
          .toList(),
    };
  }
}
