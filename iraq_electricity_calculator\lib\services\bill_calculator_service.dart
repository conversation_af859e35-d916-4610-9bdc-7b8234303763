import '../models/bill_type.dart';
import '../models/bill_calculation.dart';
import '../models/pricing_tier.dart';

class BillCalculatorService {
  static BillCalculation calculateBill({
    required BillType billType,
    required int totalKwh,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    final List<PricingTier> tiers = billType == BillType.residential
        ? PricingTiers.residential
        : PricingTiers.commercial;

    final List<TierCalculation> tierCalculations = [];
    double totalElectricityAmount = 0.0;
    int remainingKwh = totalKwh;

    for (final tier in tiers) {
      if (remainingKwh <= 0) break;

      int kwhForThisTier;
      if (tier.maxKwh == null) {
        // Last tier - use all remaining kwh
        kwhForThisTier = remainingKwh;
      } else {
        // Calculate kwh for this tier
        int tierCapacity = tier.maxKwh! - tier.minKwh + 1;
        kwhForThisTier = remainingKwh > tierCapacity ? tierCapacity : remainingKwh;
      }

      if (kwhForThisTier > 0) {
        double amount = kwhForThisTier * tier.pricePerKwh;
        tierCalculations.add(TierCalculation(
          tier: tier,
          kwhUsed: kwhForThisTier,
          amount: amount,
        ));
        totalElectricityAmount += amount;
        remainingKwh -= kwhForThisTier;
      }
    }

    final double subscriptionFee = billType.subscriptionFee;
    final double totalAmount = totalElectricityAmount + subscriptionFee;

    return BillCalculation(
      billType: billType,
      totalKwh: totalKwh,
      subscriptionFee: subscriptionFee,
      tierCalculations: tierCalculations,
      totalElectricityAmount: totalElectricityAmount,
      totalAmount: totalAmount,
      calculationDate: DateTime.now(),
      startDate: startDate,
      endDate: endDate,
    );
  }

  static List<PricingTier> getTiersForBillType(BillType billType) {
    return billType == BillType.residential
        ? PricingTiers.residential
        : PricingTiers.commercial;
  }

  static String formatCurrency(double amount) {
    return '${amount.toInt().toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )} دينار';
  }

  static double calculateMonthlyAverage(int totalKwh, int months) {
    if (months <= 0) return 0.0;
    return totalKwh / months;
  }

  static Map<String, dynamic> getBillSummary(BillCalculation calculation) {
    return {
      'billType': calculation.billTypeText,
      'totalKwh': calculation.totalKwhText,
      'subscriptionFee': formatCurrency(calculation.subscriptionFee),
      'electricityAmount': formatCurrency(calculation.totalElectricityAmount),
      'totalAmount': formatCurrency(calculation.totalAmount),
      'tierBreakdown': calculation.tierCalculations.map((tier) => {
        'tierName': tier.tier.name,
        'range': tier.tier.rangeText,
        'kwhUsed': '${tier.kwhUsed} كيلو واط ساعة',
        'pricePerKwh': tier.tier.priceText,
        'amount': formatCurrency(tier.amount),
        'color': tier.tier.color.value,
      }).toList(),
    };
  }
}
