import 'bill_type.dart';
import 'pricing_tier.dart';

class TierCalculation {
  final PricingTier tier;
  final int kwhUsed;
  final double amount;

  const TierCalculation({
    required this.tier,
    required this.kwhUsed,
    required this.amount,
  });
}

class BillCalculation {
  final BillType billType;
  final int totalKwh;
  final double subscriptionFee;
  final List<TierCalculation> tierCalculations;
  final double totalElectricityAmount;
  final double totalAmount;
  final DateTime calculationDate;
  final DateTime? startDate;
  final DateTime? endDate;

  const BillCalculation({
    required this.billType,
    required this.totalKwh,
    required this.subscriptionFee,
    required this.tierCalculations,
    required this.totalElectricityAmount,
    required this.totalAmount,
    required this.calculationDate,
    this.startDate,
    this.endDate,
  });

  String get periodText {
    if (startDate != null && endDate != null) {
      return 'من ${_formatDate(startDate!)} إلى ${_formatDate(endDate!)}';
    }
    return 'تاريخ الحساب: ${_formatDate(calculationDate)}';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String get billTypeText => billType.name;
  
  String get totalKwhText => '$totalKwh كيلو واط ساعة';
  
  String get subscriptionFeeText => '${subscriptionFee.toInt()} دينار';
  
  String get totalElectricityAmountText => '${totalElectricityAmount.toInt()} دينار';
  
  String get totalAmountText => '${totalAmount.toInt()} دينار';

  Map<String, dynamic> toJson() {
    return {
      'billType': billType.englishName,
      'totalKwh': totalKwh,
      'subscriptionFee': subscriptionFee,
      'tierCalculations': tierCalculations.map((tier) => {
        'tierName': tier.tier.name,
        'kwhUsed': tier.kwhUsed,
        'amount': tier.amount,
        'pricePerKwh': tier.tier.pricePerKwh,
      }).toList(),
      'totalElectricityAmount': totalElectricityAmount,
      'totalAmount': totalAmount,
      'calculationDate': calculationDate.toIso8601String(),
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
    };
  }
}
