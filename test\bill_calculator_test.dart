import 'package:flutter_test/flutter_test.dart';
import 'package:iraq_electricity_calculator/models/bill_type.dart';
import 'package:iraq_electricity_calculator/services/bill_calculator_service.dart';

void main() {
  group('BillCalculatorService Tests', () {
    test('حساب فاتورة منزلية عادية - 4000 كيلو واط ساعة', () {
      final result = BillCalculatorService.calculateBill(
        billType: BillType.residential,
        totalKwh: 4000,
      );

      expect(result.totalKwh, 4000);
      expect(result.subscriptionFee, 2000);
      expect(result.totalElectricityAmount, 65000); // 3000*10 + 1000*35
      expect(result.totalAmount, 67000); // 65000 + 2000
      expect(result.tierCalculations.length, 2);
    });

    test('حساب فاتورة تجارية عادية - 3000 كيلو واط ساعة', () {
      final result = BillCalculatorService.calculateBill(
        billType: BillType.commercial,
        totalKwh: 3000,
      );

      expect(result.totalKwh, 3000);
      expect(result.subscriptionFee, 5000);
      expect(result.totalElectricityAmount, 200000); // 2000*60 + 1000*80
      expect(result.totalAmount, 205000); // 200000 + 5000
      expect(result.tierCalculations.length, 2);
    });

    test('حساب فاتورة منزلية لفترة طويلة - 12000 كيلو واط ساعة لـ 7 أشهر', () {
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 8, 1); // 7 أشهر

      final result = BillCalculatorService.calculateBill(
        billType: BillType.residential,
        totalKwh: 12000,
        startDate: startDate,
        endDate: endDate,
      );

      expect(result.totalKwh, 12000);
      expect(result.isLongPeriod, true);
      expect(result.numberOfPeriods, 4); // 7 أشهر ÷ 2 = 3.5 → 4 فترات
      expect(result.subscriptionFee, 8000); // 4 فترات × 2000
      
      // كل فترة: 3000 كيلو واط ساعة = 30000 دينار
      // 4 فترات × 30000 = 120000 دينار
      expect(result.totalElectricityAmount, 120000);
      expect(result.totalAmount, 128000); // 120000 + 8000
    });

    test('حساب فاتورة تجارية لفترة طويلة - 16000 كيلو واط ساعة لـ 6 أشهر', () {
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 7, 1); // 6 أشهر

      final result = BillCalculatorService.calculateBill(
        billType: BillType.commercial,
        totalKwh: 16000,
        startDate: startDate,
        endDate: endDate,
      );

      expect(result.totalKwh, 16000);
      expect(result.isLongPeriod, true);
      expect(result.numberOfPeriods, 3); // 6 أشهر ÷ 2 = 3 فترات
      expect(result.subscriptionFee, 15000); // 3 فترات × 5000
      
      // كل فترة: 5333 كيلو واط ساعة تقريباً
      // الشريحة الأولى: 2000 × 60 = 120000
      // الشريحة الثانية: 2000 × 80 = 160000  
      // الشريحة الثالثة: 1333 × 120 = 159960
      // إجمالي كل فترة: 439960
      // 3 فترات × 439960 = 1319880 (تقريباً)
      expect(result.totalElectricityAmount, greaterThan(1300000));
      expect(result.totalAmount, greaterThan(1315000));
    });

    test('حساب فاتورة لفترة قصيرة - شهرين', () {
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 3, 1); // شهرين

      final result = BillCalculatorService.calculateBill(
        billType: BillType.residential,
        totalKwh: 6000,
        startDate: startDate,
        endDate: endDate,
      );

      expect(result.totalKwh, 6000);
      expect(result.isLongPeriod, false);
      expect(result.numberOfPeriods, 1);
      expect(result.subscriptionFee, 2000); // فترة واحدة
      
      // 3000*10 + 3000*35 = 30000 + 105000 = 135000
      expect(result.totalElectricityAmount, 135000);
      expect(result.totalAmount, 137000); // 135000 + 2000
    });

    test('حساب فاتورة لفترة شهر واحد', () {
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 2, 1); // شهر واحد

      final result = BillCalculatorService.calculateBill(
        billType: BillType.residential,
        totalKwh: 2000,
        startDate: startDate,
        endDate: endDate,
      );

      expect(result.totalKwh, 2000);
      expect(result.isLongPeriod, false);
      expect(result.numberOfPeriods, 1);
      expect(result.subscriptionFee, 2000);
      
      // 2000*10 = 20000
      expect(result.totalElectricityAmount, 20000);
      expect(result.totalAmount, 22000); // 20000 + 2000
    });

    test('تحقق من تسمية الشرائح للفترات الطويلة', () {
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 5, 1); // 4 أشهر

      final result = BillCalculatorService.calculateBill(
        billType: BillType.residential,
        totalKwh: 8000,
        startDate: startDate,
        endDate: endDate,
      );

      expect(result.isLongPeriod, true);
      expect(result.numberOfPeriods, 2); // 4 أشهر ÷ 2 = 2 فترة
      
      // يجب أن تحتوي أسماء الشرائح على رقم الفترة
      final tierNames = result.tierCalculations.map((t) => t.tier.name).toList();
      expect(tierNames.any((name) => name.contains('الفترة 1')), true);
      expect(tierNames.any((name) => name.contains('الفترة 2')), true);
    });
  });
}
